import { Mock<PERSON>ethod } from 'vite-plugin-mock';
import { generatePermission } from '../src/routes';

export default [
  // 用户信息
  {
    url: '/fe/api/user/userInfo',
    method: 'get',
    response: () => {
      console.log('UserInfo mock called via vite-plugin-mock');
      const userRole = 'admin';
      return {
        name: 'admin',
        avatar:
          'https://lf1-xgcdn-tos.pstatp.com/obj/vcloud/vadmin/start.8e0e4855ee346a46ccff8ff3e24db27b.png',
        email: '<EMAIL>',
        job: 'frontend',
        jobName: '前端开发工程师',
        organization: 'Frontend',
        organizationName: '前端',
        location: 'beijing',
        locationName: '北京',
        introduction: '王力群并非是一个真实存在的人。',
        personalWebsite: 'https://www.arco.design',
        verified: true,
        phoneNumber: '177******02',
        accountId: 'abcd-********',
        registrationTime: '2021-01-01 12:00:00',
        permissions: generatePermission(userRole),
      };
    },
  },
  // 登录
  {
    url: '/fe/api/user/login',
    method: 'post',
    response: ({ body }: { body: any }) => {
      console.log('Login mock intercepted via vite-plugin-mock:', body);
      const { userName, password } = body;
      if (!userName) {
        return {
          status: 'error',
          msg: '用户名不能为空',
        };
      }
      if (!password) {
        return {
          status: 'error',
          msg: '密码不能为空',
        };
      }
      if (userName === 'admin' && password === 'admin') {
        return {
          status: 'ok',
        };
      }
      return {
        status: 'error',
        msg: '账号或者密码错误',
      };
    },
  },
] as MockMethod[];
